---
title: CSS布局技巧
date: 2023-07-18
author: 朱鹏亮
tags: [CSS, 前端, 布局]
---

# CSS布局技巧

CSS布局是前端开发中的重要部分，掌握好布局技巧可以让你的网页设计更加灵活和专业。

## Flexbox布局

Flexbox是一种一维布局模型，它提供了强大的空间分布和对齐能力。

### 基本概念

```css
.container {
  display: flex;
  flex-direction: row; /* 或 column */
  justify-content: space-between; /* 主轴对齐 */
  align-items: center; /* 交叉轴对齐 */
}
```

### 常用属性

- `flex-direction`: 定义主轴方向
- `justify-content`: 主轴对齐方式
- `align-items`: 交叉轴对齐方式
- `flex-wrap`: 是否换行
- `flex-grow`: 放大比例
- `flex-shrink`: 缩小比例
- `flex-basis`: 初始大小

### 实例

```css
.card-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.card {
  flex: 1 1 300px; /* grow shrink basis */
}
```

## Grid布局

Grid是一种二维布局系统，它可以同时处理行和列。

### 基本语法

```css
.container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: auto;
  gap: 20px;
}
```

### 常用属性

- `grid-template-columns`: 定义列的大小和数量
- `grid-template-rows`: 定义行的大小和数量
- `gap`: 网格间距
- `grid-column`: 指定项目的列位置
- `grid-row`: 指定项目的行位置

### 实例

```css
.dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  grid-auto-rows: minmax(100px, auto);
  gap: 20px;
}

.feature {
  grid-column: span 2;
}
```

## 响应式布局

### 媒体查询

```css
@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }
}
```

### 视口单位

- `vw`: 视口宽度的1%
- `vh`: 视口高度的1%
- `vmin`: vw和vh中的较小值
- `vmax`: vw和vh中的较大值

```css
.hero {
  height: 100vh;
  font-size: calc(16px + 1vw);
}
```

## 定位技巧

### 绝对居中

```css
.center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
```

### 粘性定位

```css
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 100;
}
```

## 实用布局模式

### 圣杯布局

```css
.holy-grail {
  display: grid;
  grid-template: 
    "header header header" auto
    "nav main aside" 1fr
    "footer footer footer" auto
    / auto 1fr auto;
  min-height: 100vh;
}
```

### 卡片网格

```css
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}
```

## 总结

CSS布局技术在不断发展，从早期的表格布局、浮动布局，到现在的Flexbox和Grid布局，为我们提供了越来越强大的工具。掌握这些技巧，可以让你的网页设计更加灵活和专业。
