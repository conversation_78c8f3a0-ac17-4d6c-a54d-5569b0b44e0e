'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import {
  ChevronRight,
  ChevronDown,
  Folder,
  FolderOpen,
  FileText,
  File,
  Eye,
  ExternalLink
} from 'lucide-react';
import SpeechButton from './SpeechButton';

export interface FileItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  extension?: string;
  children?: FileItem[];
}

interface FileListProps {
  files: FileItem[];
  onFileSelect: (filePath: string) => void;
  selectedFile?: string;
}

interface FileItemProps {
  item: FileItem;
  level: number;
  onFileSelect: (filePath: string) => void;
  selectedFile?: string;
}

// 获取文件图标
const getFileIcon = (extension?: string) => {
  switch (extension) {
    case 'md':
    case 'markdown':
      return <FileText className="w-4 h-4 text-blue-500" />;
    case 'docx':
    case 'doc':
      return <File className="w-4 h-4 text-blue-600" />;
    case 'txt':
      return <FileText className="w-4 h-4 text-gray-500" />;
    default:
      return <File className="w-4 h-4 text-gray-400" />;
  }
};

// 单个文件/文件夹项组件
const FileItemComponent: React.FC<FileItemProps> = ({
  item,
  level,
  onFileSelect,
  selectedFile
}) => {
  // 第一级文件夹默认展开，其他级别默认收起
  const [isExpanded, setIsExpanded] = useState(level === 0);

  const handleClick = () => {
    if (item.type === 'directory') {
      setIsExpanded(!isExpanded);
    } else {
      onFileSelect(item.path);
    }
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    if (item.type === 'file') {
      e.preventDefault();
      // 右键菜单功能可以在这里实现
    }
  };

  const getReadPath = () => {
    // 移除 'blog/' 前缀来构建阅读路径
    const path = item.path.replace(/^blog\//, '');
    // 直接返回路径，让Next.js自动处理编码
    return path;
  };
  
  const isSelected = selectedFile === item.path;
  const paddingLeft = level * 20 + 8;
  
  return (
    <div>
      <div
        className={`
          file-item flex items-center py-2.5 px-3 cursor-pointer rounded-md group
          ${isSelected ? 'selected' : ''}
        `}
        style={{
          paddingLeft: `${paddingLeft}px`,
          '--padding-left': `${paddingLeft}px`
        } as React.CSSProperties & { '--padding-left': string }}
        onClick={handleClick}
        onContextMenu={handleContextMenu}
      >
        {/* 展开/收起图标 */}
        {item.type === 'directory' && (
          <div className="mr-1">
            {isExpanded ? (
              <ChevronDown className="w-4 h-4 text-gray-500" />
            ) : (
              <ChevronRight className="w-4 h-4 text-gray-500" />
            )}
          </div>
        )}

        {/* 文件/文件夹图标 */}
        <div className="mr-2">
          {item.type === 'directory' ? (
            isExpanded ? (
              <FolderOpen className="w-4 h-4 text-yellow-500" />
            ) : (
              <Folder className="w-4 h-4 text-yellow-600" />
            )
          ) : (
            getFileIcon(item.extension)
          )}
        </div>

        {/* 文件/文件夹名称 */}
        <span className={`
          text-sm truncate flex-grow
          ${item.type === 'directory' ? 'font-medium text-gray-700' : 'text-gray-600'}
          ${isSelected ? 'text-blue-700 font-medium' : ''}
        `}>
          {item.name}
        </span>

        {/* 文件操作按钮 */}
        {item.type === 'file' && (
          <div className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity flex items-center gap-1">
            <Link
              href={`/read/${getReadPath()}`}
              onClick={(e) => e.stopPropagation()}
              className="p-1 hover:bg-blue-100 rounded text-blue-600 hover:text-blue-800 transition-colors"
              title="阅读模式"
            >
              <Eye className="w-3 h-3" />
            </Link>
            <button
              onClick={(e) => {
                e.stopPropagation();
                window.open(`/read/${getReadPath()}`, '_blank');
              }}
              className="p-1 hover:bg-gray-100 rounded text-gray-600 hover:text-gray-800 transition-colors"
              title="新窗口打开"
            >
              <ExternalLink className="w-3 h-3" />
            </button>
          </div>
        )}
      </div>
      
      {/* 子项目 */}
      {item.type === 'directory' && isExpanded && item.children && (
        <div>
          {item.children.map((child, index) => (
            <FileItemComponent
              key={`${child.path}-${index}`}
              item={child}
              level={level + 1}
              onFileSelect={onFileSelect}
              selectedFile={selectedFile}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// 计算文件总数的辅助函数
const countFiles = (items: FileItem[]): number => {
  return items.reduce((count, item) => {
    if (item.type === 'file') {
      return count + 1;
    } else if (item.children) {
      return count + countFiles(item.children);
    }
    return count;
  }, 0);
};

// 主文件列表组件
const FileList: React.FC<FileListProps> = ({
  files,
  onFileSelect,
  selectedFile
}) => {
  const totalFiles = countFiles(files);

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm h-full flex flex-col card-hover">
      <div className="p-4 border-b border-gray-200 flex items-center justify-between bg-gradient-to-r from-blue-50 to-indigo-50 flex-shrink-0">
        <h2 className="text-lg font-semibold gradient-text">博客文章</h2>
        <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">
          {totalFiles} 个文件
        </span>
      </div>

      <div className="p-3 overflow-y-auto flex-grow custom-scrollbar min-h-0">
        {files.length === 0 ? (
          <div className="text-center py-12 text-gray-500">
            <Folder className="w-16 h-16 mx-auto mb-4 text-gray-300" />
            <p className="text-sm">暂无文件</p>
            <p className="text-xs text-gray-400 mt-2">请在 public/blog 文件夹中添加文件</p>
          </div>
        ) : (
          <div className="space-y-1">
            {files.map((file, index) => (
              <FileItemComponent
                key={`${file.path}-${index}`}
                item={file}
                level={0}
                onFileSelect={onFileSelect}
                selectedFile={selectedFile}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default FileList;
