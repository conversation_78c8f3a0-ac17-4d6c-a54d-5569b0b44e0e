'use client';

import React, { useState, useEffect } from 'react';
import { List, ChevronRight } from 'lucide-react';

interface TocItem {
  id: string;
  text: string;
  level: number;
}

interface TableOfContentsProps {
  content: string;
  className?: string;
}

export default function TableOfContents({ content, className = '' }: TableOfContentsProps) {
  const [tocItems, setTocItems] = useState<TocItem[]>([]);
  const [activeId, setActiveId] = useState<string>('');
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    // 解析标题
    const headings = extractHeadings(content);
    setTocItems(headings);

    // 为标题添加ID
    addHeadingIds();
  }, [content]);

  useEffect(() => {
    // 监听滚动，更新活跃标题
    const handleScroll = () => {
      const headingElements = tocItems.map(item => document.getElementById(item.id)).filter(Boolean);

      let currentActiveId = '';

      for (let i = headingElements.length - 1; i >= 0; i--) {
        const element = headingElements[i];
        if (element && element.getBoundingClientRect().top <= 100) {
          currentActiveId = element.id;
          break;
        }
      }

      setActiveId(currentActiveId);
    };

    const throttledScroll = throttle(handleScroll, 100);
    window.addEventListener('scroll', throttledScroll);

    return () => window.removeEventListener('scroll', throttledScroll);
  }, [tocItems]);

  const extractHeadings = (markdownContent: string): TocItem[] => {
    const headingRegex = /^(#{1,6})\s+(.+)$/gm;
    const headings: TocItem[] = [];
    let match;

    while ((match = headingRegex.exec(markdownContent)) !== null) {
      const level = match[1].length;
      const text = match[2].trim();
      const id = generateId(text);

      headings.push({
        id,
        text,
        level,
      });
    }

    return headings;
  };

  const generateId = (text: string): string => {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // 移除特殊字符
      .replace(/\s+/g, '-') // 空格替换为连字符
      .trim();
  };

  const addHeadingIds = () => {
    // 在DOM加载后为标题添加ID
    setTimeout(() => {
      tocItems.forEach(item => {
        const headingElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        headingElements.forEach(element => {
          if (element.textContent?.trim() === item.text) {
            element.id = item.id;
          }
        });
      });
    }, 100);
  };

  const scrollToHeading = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      const offsetTop = element.getBoundingClientRect().top + window.pageYOffset - 80;
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth',
      });
    }
    setIsOpen(false);
  };

  const throttle = (func: Function, limit: number) => {
    let inThrottle: boolean;
    return function (this: any, ...args: any[]) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  };

  if (tocItems.length === 0) return null;

  return (
    <>
      {/* 移动端切换按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className='fixed bottom-6 right-6 z-40 lg:hidden bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors'
      >
        <List className='w-5 h-5' />
      </button>

      {/* 目录面板 */}
      <div
        className={`
        fixed top-20 right-6 w-64 max-h-96 bg-white border border-gray-200 rounded-lg shadow-lg z-30
        transition-all duration-300 overflow-hidden
        ${isOpen ? 'opacity-100 visible' : 'opacity-0 invisible lg:opacity-100 lg:visible'}
        ${className}
      `}
      >
        <div className='p-4 border-b border-gray-200'>
          <div className='flex items-center justify-between'>
            <h3 className='font-semibold text-gray-800'>目录</h3>
            <button onClick={() => setIsOpen(false)} className='lg:hidden text-gray-500 hover:text-gray-700'>
              ×
            </button>
          </div>
        </div>

        <div className='max-h-80 overflow-y-auto p-2'>
          {tocItems.map((item, index) => (
            <button
              key={index}
              onClick={() => scrollToHeading(item.id)}
              className={`
                w-full text-left px-3 py-2 rounded-md text-sm transition-colors
                hover:bg-gray-100 flex items-center gap-2
                ${activeId === item.id ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-600'}
              `}
              style={{ paddingLeft: `${(item.level - 1) * 12 + 12}px` }}
            >
              {item.level > 1 && <ChevronRight className='w-3 h-3 flex-shrink-0' />}
              <span className='truncate'>{item.text}</span>
            </button>
          ))}
        </div>
      </div>

      {/* 移动端遮罩 */}
      {isOpen && <div className='fixed inset-0 bg-opacity-20 z-20 lg:hidden' onClick={() => setIsOpen(false)} />}
    </>
  );
}
