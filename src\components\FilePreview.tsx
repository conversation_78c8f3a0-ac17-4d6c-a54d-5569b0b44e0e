'use client';

import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { FileText, Calendar, User, Tag, Loader2, AlertCircle, Eye, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import SpeechButton from './SpeechButton';

interface FileContent {
  type: 'markdown' | 'word' | 'text';
  content: string;
  fileName: string;
  frontMatter?: {
    title?: string;
    date?: string;
    author?: string;
    tags?: string[];
    category?: string;
    [key: string]: any;
  };
  messages?: any[];
}

interface FilePreviewProps {
  filePath?: string;
}

const FilePreview: React.FC<FilePreviewProps> = ({ filePath }) => {
  const [content, setContent] = useState<FileContent | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取阅读路径
  const getReadPath = () => {
    if (!filePath) return '';
    return filePath.replace(/^blog\//, '');
  };

  useEffect(() => {
    if (filePath) {
      fetchFileContent(filePath);
    } else {
      setContent(null);
      setError(null);
    }
  }, [filePath]);

  const fetchFileContent = async (path: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/file-content?path=${encodeURIComponent(path)}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch file content');
      }
      
      const data = await response.json();
      setContent(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  // 格式化日期
  const formatDate = (dateString?: string) => {
    if (!dateString) return null;
    
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  // 渲染文件元信息
  const renderMetadata = (frontMatter?: FileContent['frontMatter']) => {
    if (!frontMatter) return null;

    return (
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 mb-8 card-hover">
        {frontMatter.title && (
          <h1 className="text-3xl font-bold gradient-text mb-4">
            {frontMatter.title}
          </h1>
        )}

        <div className="flex flex-wrap gap-6 text-sm text-gray-600 mb-4">
          {frontMatter.author && (
            <div className="flex items-center gap-2 bg-white px-3 py-1 rounded-full shadow-sm">
              <User className="w-4 h-4 text-blue-500" />
              <span className="font-medium">{frontMatter.author}</span>
            </div>
          )}

          {frontMatter.date && (
            <div className="flex items-center gap-2 bg-white px-3 py-1 rounded-full shadow-sm">
              <Calendar className="w-4 h-4 text-green-500" />
              <span>{formatDate(frontMatter.date)}</span>
            </div>
          )}

          {frontMatter.category && (
            <div className="flex items-center gap-2 bg-white px-3 py-1 rounded-full shadow-sm">
              <FileText className="w-4 h-4 text-purple-500" />
              <span>{frontMatter.category}</span>
            </div>
          )}
        </div>

        {frontMatter.tags && frontMatter.tags.length > 0 && (
          <div className="flex items-start gap-3 mb-4">
            <Tag className="w-4 h-4 text-gray-500 mt-1" />
            <div className="flex flex-wrap gap-2">
              {frontMatter.tags.map((tag, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-gradient-to-r from-blue-500 to-indigo-500 text-white text-xs rounded-full shadow-sm hover:shadow-md transition-shadow"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex items-center gap-3 pt-4 border-t border-blue-200">
          <Link
            href={`/read/${getReadPath()}`}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Eye className="w-4 h-4" />
            阅读模式
          </Link>

          <Link
            href={`/read/${getReadPath()}`}
            target="_blank"
            className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <ExternalLink className="w-4 h-4" />
            新窗口
          </Link>

          {content?.content && (
            <SpeechButton
              text={content.content}
              className="px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
            />
          )}
        </div>
      </div>
    );
  };

  // 渲染内容
  const renderContent = () => {
    if (!content) return null;

    switch (content.type) {
      case 'markdown':
        return (
          <div className="prose prose-gray max-w-none">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                // 自定义代码块样式
                code: ({ node, inline, className, children, ...props }) => {
                  const match = /language-(\w+)/.exec(className || '');
                  return !inline && match ? (
                    <pre className="bg-gray-100 rounded-lg p-4 overflow-x-auto">
                      <code className={className} {...props}>
                        {children}
                      </code>
                    </pre>
                  ) : (
                    <code className="bg-gray-100 px-1 py-0.5 rounded text-sm" {...props}>
                      {children}
                    </code>
                  );
                },
                // 自定义表格样式
                table: ({ children }) => (
                  <div className="overflow-x-auto">
                    <table className="min-w-full border border-gray-300">
                      {children}
                    </table>
                  </div>
                ),
                th: ({ children }) => (
                  <th className="border border-gray-300 px-4 py-2 bg-gray-50 font-semibold text-left">
                    {children}
                  </th>
                ),
                td: ({ children }) => (
                  <td className="border border-gray-300 px-4 py-2">
                    {children}
                  </td>
                ),
              }}
            >
              {content.content}
            </ReactMarkdown>
          </div>
        );
      
      case 'word':
        return (
          <div 
            className="prose prose-gray max-w-none"
            dangerouslySetInnerHTML={{ __html: content.content }}
          />
        );
      
      case 'text':
        return (
          <pre className="whitespace-pre-wrap font-mono text-sm bg-gray-50 p-4 rounded-lg border">
            {content.content}
          </pre>
        );
      
      default:
        return (
          <div className="text-center py-8 text-gray-500">
            <AlertCircle className="w-12 h-12 mx-auto mb-2 text-gray-300" />
            <p>不支持的文件类型</p>
          </div>
        );
    }
  };

  if (!filePath) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg shadow-sm h-full flex items-center justify-center">
        <div className="text-center text-gray-500 max-w-md mx-auto p-6">
          <FileText className="w-16 h-16 mx-auto mb-4 text-gray-300" />
          <h3 className="text-xl font-medium mb-2">选择一个文件来预览</h3>
          <p className="text-sm text-gray-400">从左侧文件列表中选择一个文件来查看其内容</p>
          <p className="mt-4 text-xs text-gray-400">支持 Markdown 和 Word 文件格式</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg shadow-sm h-full flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <Loader2 className="w-10 h-10 mx-auto mb-4 text-blue-500 animate-spin" />
          <p className="text-gray-600">正在加载文件内容...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg shadow-sm h-full flex items-center justify-center">
        <div className="text-center text-red-500 max-w-md mx-auto p-6">
          <AlertCircle className="w-12 h-12 mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">加载失败</h3>
          <p className="text-sm">{error}</p>
          <button
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            onClick={() => fetchFileContent(filePath)}
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm h-full overflow-hidden flex flex-col card-hover relative">
      <div className="p-6 overflow-y-auto custom-scrollbar flex-grow min-h-0">
        {renderMetadata(content?.frontMatter)}
        <div>
          {renderContent()}
        </div>
      </div>

      {/* 浮动阅读模式按钮 */}
      {content && (
        <div className="absolute top-4 right-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <Link
            href={`/read/${getReadPath()}`}
            className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-lg"
            title="进入阅读模式"
          >
            <Eye className="w-4 h-4" />
            <span className="text-sm">阅读模式</span>
          </Link>
        </div>
      )}

      {/* 悬停时显示的操作栏 */}
      {content && (
        <div className="absolute bottom-4 right-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <SpeechButton
            text={content.content}
            className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors shadow-lg"
            size="sm"
          />
          <Link
            href={`/read/${getReadPath()}`}
            target="_blank"
            className="p-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors shadow-lg"
            title="新窗口打开"
          >
            <ExternalLink className="w-4 h-4" />
          </Link>
        </div>
      )}
    </div>
  );
};

export default FilePreview;
