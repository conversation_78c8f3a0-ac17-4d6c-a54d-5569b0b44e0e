import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import mammoth from 'mammoth';
import matter from 'gray-matter';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    let filePath = searchParams.get('path');

    if (!filePath) {
      return NextResponse.json(
        { error: 'File path is required' },
        { status: 400 }
      );
    }

    // 处理可能的双重编码问题
    try {
      // 如果路径包含%25，说明被双重编码了，需要先解码一次
      if (filePath.includes('%25')) {
        filePath = decodeURIComponent(filePath);
      }
      // 再解码一次确保中文字符正确
      filePath = decodeURIComponent(filePath);
    } catch (error) {
      // 如果解码失败，使用原始路径
      console.warn('Failed to decode path:', filePath, error);
    }
    
    // 构建完整路径（相对于public文件夹）
    const fullPath = path.join(process.cwd(), 'public', filePath);
    
    // 检查文件是否存在
    if (!fs.existsSync(fullPath)) {
      return NextResponse.json(
        { error: 'File does not exist' },
        { status: 404 }
      );
    }
    
    // 检查是否是文件
    const stats = fs.statSync(fullPath);
    if (!stats.isFile()) {
      return NextResponse.json(
        { error: 'Path is not a file' },
        { status: 400 }
      );
    }
    
    const extension = path.extname(fullPath).toLowerCase();
    
    // 处理不同类型的文件
    switch (extension) {
      case '.md':
      case '.markdown':
        return handleMarkdownFile(fullPath);
      
      case '.docx':
        return handleWordFile(fullPath);
      
      case '.txt':
        return handleTextFile(fullPath);
      
      default:
        return NextResponse.json(
          { error: 'Unsupported file type' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error reading file content:', error);
    return NextResponse.json(
      { error: 'Failed to read file content' },
      { status: 500 }
    );
  }
}

// 处理Markdown文件
async function handleMarkdownFile(filePath: string) {
  const fileContent = fs.readFileSync(filePath, 'utf-8');
  
  // 使用gray-matter解析front matter
  const { data: frontMatter, content } = matter(fileContent);
  
  return NextResponse.json({
    type: 'markdown',
    content,
    frontMatter,
    fileName: path.basename(filePath)
  });
}

// 处理Word文件
async function handleWordFile(filePath: string) {
  const buffer = fs.readFileSync(filePath);
  
  try {
    const result = await mammoth.convertToHtml({ buffer });
    
    return NextResponse.json({
      type: 'word',
      content: result.value,
      fileName: path.basename(filePath),
      messages: result.messages // 转换过程中的警告或错误信息
    });
  } catch (error) {
    console.error('Error converting Word file:', error);
    return NextResponse.json(
      { error: 'Failed to convert Word file' },
      { status: 500 }
    );
  }
}

// 处理纯文本文件
async function handleTextFile(filePath: string) {
  const content = fs.readFileSync(filePath, 'utf-8');
  
  return NextResponse.json({
    type: 'text',
    content,
    fileName: path.basename(filePath)
  });
}
