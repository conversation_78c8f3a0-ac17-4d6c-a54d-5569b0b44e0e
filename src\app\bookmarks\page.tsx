'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  Bookmark, 
  ArrowLeft, 
  FileText, 
  Calendar, 
  User, 
  Trash2,
  Eye,
  ExternalLink
} from 'lucide-react';

interface BookmarkItem {
  path: string;
  title?: string;
  author?: string;
  date?: string;
  addedAt: string;
}

export default function BookmarksPage() {
  const [bookmarks, setBookmarks] = useState<BookmarkItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadBookmarks();
  }, []);

  const loadBookmarks = async () => {
    setLoading(true);
    
    try {
      const bookmarkPaths = JSON.parse(localStorage.getItem('bookmarks') || '[]');
      const bookmarkItems: BookmarkItem[] = [];

      for (const path of bookmarkPaths) {
        try {
          const response = await fetch(`/api/file-content?path=${encodeURIComponent(path)}`);
          if (response.ok) {
            const data = await response.json();
            bookmarkItems.push({
              path,
              title: data.frontMatter?.title || data.fileName,
              author: data.frontMatter?.author,
              date: data.frontMatter?.date,
              addedAt: new Date().toISOString() // 实际应用中应该保存真实的添加时间
            });
          }
        } catch (error) {
          console.error(`Failed to load bookmark ${path}:`, error);
        }
      }

      setBookmarks(bookmarkItems);
    } catch (error) {
      console.error('Failed to load bookmarks:', error);
    } finally {
      setLoading(false);
    }
  };

  const removeBookmark = (path: string) => {
    const currentBookmarks = JSON.parse(localStorage.getItem('bookmarks') || '[]');
    const newBookmarks = currentBookmarks.filter((p: string) => p !== path);
    localStorage.setItem('bookmarks', JSON.stringify(newBookmarks));
    
    setBookmarks(prev => prev.filter(item => item.path !== path));
  };

  const getReadPath = (path: string) => {
    return path.replace(/^blog\//, '');
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return null;
    
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <Link 
                href="/" 
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </Link>
              <div className="p-2 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg">
                <Bookmark className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold gradient-text">我的收藏</h1>
                <p className="text-xs text-gray-500 -mt-1">Bookmarks</p>
              </div>
            </div>
            <div className="text-sm text-gray-500 bg-gray-50 px-3 py-1 rounded-full">
              {bookmarks.length} 篇文章
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">正在加载收藏...</p>
          </div>
        ) : bookmarks.length === 0 ? (
          <div className="text-center py-16">
            <Bookmark className="w-16 h-16 mx-auto mb-4 text-gray-300" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无收藏</h3>
            <p className="text-gray-500 mb-6">开始收藏你喜欢的文章吧</p>
            <Link
              href="/"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              浏览文章
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            {bookmarks.map((bookmark, index) => (
              <div
                key={bookmark.path}
                className="bg-white border border-gray-200 rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-grow">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {bookmark.title || '无标题'}
                    </h3>
                    
                    <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-3">
                      {bookmark.author && (
                        <div className="flex items-center gap-1">
                          <User className="w-4 h-4" />
                          <span>{bookmark.author}</span>
                        </div>
                      )}
                      
                      {bookmark.date && (
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          <span>{formatDate(bookmark.date)}</span>
                        </div>
                      )}
                      
                      <div className="flex items-center gap-1">
                        <FileText className="w-4 h-4" />
                        <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                          {bookmark.path}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <Link
                        href={`/read/${getReadPath(bookmark.path)}`}
                        className="inline-flex items-center gap-2 px-3 py-1.5 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <Eye className="w-4 h-4" />
                        阅读
                      </Link>
                      
                      <Link
                        href={`/read/${getReadPath(bookmark.path)}`}
                        target="_blank"
                        className="inline-flex items-center gap-2 px-3 py-1.5 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors"
                      >
                        <ExternalLink className="w-4 h-4" />
                        新窗口
                      </Link>
                    </div>
                  </div>

                  <button
                    onClick={() => removeBookmark(bookmark.path)}
                    className="ml-4 p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    title="移除收藏"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </main>
    </div>
  );
}
