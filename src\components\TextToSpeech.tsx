'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Play,
  Pause,
  Square,
  Ski<PERSON>For<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Setting<PERSON>,
  Headphones,
  X
} from 'lucide-react';

interface TextToSpeechProps {
  content: string;
  className?: string;
  currentFilePath?: string;
  onAutoNext?: () => void;
  onGetNextContent?: () => Promise<{ content: string; title: string; path: string } | null>;
  onAppendContent?: (nextContent: { content: string; title: string; path: string }) => void;
}

interface SpeechSettings {
  rate: number;
  pitch: number;
  volume: number;
  voice: string;
}

export default function TextToSpeech({
  content,
  className = '',
  onAutoNext,
  onGetNextContent,
  onAppendContent
}: TextToSpeechProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [currentPosition, setCurrentPosition] = useState(0);
  const [totalLength, setTotalLength] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [availableVoices, setAvailableVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [settings, setSettings] = useState<SpeechSettings>({
    rate: 1,
    pitch: 1,
    volume: 0.8,
    voice: ''
  });

  const [autoNext, setAutoNext] = useState(false);
  const [selectedText, setSelectedText] = useState<string>('');
  const [currentArticleTitle, setCurrentArticleTitle] = useState<string>('');

  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);
  const textChunksRef = useRef<string[]>([]);
  const currentChunkRef = useRef(0);
  const originalContentRef = useRef<string>('');

  // 在客户端加载时恢复设置
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // 恢复语音设置
      const savedSettings = localStorage.getItem('speechSettings');
      if (savedSettings) {
        try {
          const parsed = JSON.parse(savedSettings);
          setSettings(parsed);
        } catch {
          console.warn('Failed to parse saved speech settings');
        }
      }

      // 恢复自动跳转设置
      const savedAutoNext = localStorage.getItem('speechAutoNext');
      if (savedAutoNext === 'true') {
        setAutoNext(true);
      }
    }
  }, []);

  useEffect(() => {
    // 检查浏览器支持
    if (!('speechSynthesis' in window)) {
      console.warn('浏览器不支持语音合成');
      return;
    }

    // 加载可用语音
    const loadVoices = () => {
      const voices = speechSynthesis.getVoices();
      setAvailableVoices(voices);

      // 优先选择中文语音
      const chineseVoice = voices.find(voice =>
        voice.lang.includes('zh') || voice.name.includes('Chinese')
      );
      if (chineseVoice && !settings.voice) {
        setSettings(prev => ({ ...prev, voice: chineseVoice.name }));
      }
    };

    loadVoices();
    speechSynthesis.addEventListener('voiceschanged', loadVoices);

    return () => {
      speechSynthesis.removeEventListener('voiceschanged', loadVoices);
      stopSpeech();
    };
  }, [settings.voice]);

  const prepareTextContent = useCallback(() => {
    if (!content) return;

    console.log('🔄 prepareTextContent 被调用，当前段落索引:', currentChunkRef.current);

    // 清理Markdown和HTML格式，提取纯文本
    let cleanText = content
      .replace(/^---[\s\S]*?---/m, '') // 移除Front Matter
      .replace(/!\[.*?\]\(.*?\)/g, '') // 移除图片
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 移除链接，保留文本
      .replace(/`{1,3}[^`]*`{1,3}/g, '') // 移除代码块
      .replace(/#{1,6}\s*/g, '') // 移除标题标记
      .replace(/[*_]{1,2}([^*_]+)[*_]{1,2}/g, '$1') // 移除粗体斜体标记
      .replace(/^\s*[-*+]\s+/gm, '') // 移除列表标记
      .replace(/^\s*\d+\.\s+/gm, '') // 移除数字列表标记
      // 清理HTML标签（用于.docx文件）
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // 移除script标签
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '') // 移除style标签
      .replace(/<[^>]+>/g, '') // 移除所有HTML标签
      .replace(/&nbsp;/g, ' ') // 替换HTML空格
      .replace(/&lt;/g, '<') // 替换HTML实体
      .replace(/&gt;/g, '>') // 替换HTML实体
      .replace(/&amp;/g, '&') // 替换HTML实体
      .replace(/&quot;/g, '"') // 替换HTML实体
      .replace(/&#39;/g, "'") // 替换HTML实体
      .replace(/\n{3,}/g, '\n\n') // 合并多个换行
      .trim();

    // 如果有选中文本，从选中位置开始
    if (selectedText && cleanText.includes(selectedText)) {
      const startIndex = cleanText.indexOf(selectedText);
      cleanText = cleanText.substring(startIndex);
    }

    // 按句子分割文本，每个chunk不超过200字符
    const sentences = cleanText.split(/[。！？.!?]\s*/);
    const chunks: string[] = [];
    let currentChunk = '';

    sentences.forEach(sentence => {
      if (sentence.trim()) {
        if (currentChunk.length + sentence.length > 200) {
          if (currentChunk) {
            chunks.push(currentChunk.trim());
            currentChunk = sentence;
          } else {
            chunks.push(sentence.trim());
          }
        } else {
          currentChunk += (currentChunk ? '。' : '') + sentence;
        }
      }
    });

    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }

    textChunksRef.current = chunks;
    setTotalLength(chunks.length);
    console.log('文本准备完成，段落数量:', chunks.length);
    console.log('选中文本:', selectedText ? `"${selectedText.substring(0, 30)}..."` : '无');
  }, [content, selectedText]);

  useEffect(() => {
    // 准备文本内容
    prepareTextContent();
  }, [prepareTextContent]);

  useEffect(() => {
    // 只在客户端运行
    if (typeof window === 'undefined') return;

    // 组件初始化时清除任何现有选择
    setSelectedText('');
    window.getSelection()?.removeAllRanges();

    // 监听文本选择
    const handleSelectionChange = () => {
      const selection = window.getSelection();
      if (selection && selection.toString().trim()) {
        setSelectedText(selection.toString().trim());
      } else {
        setSelectedText('');
      }
    };

    document.addEventListener('selectionchange', handleSelectionChange);

    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
    };
  }, []);

  const createUtterance = useCallback((text: string) => {
    const utterance = new SpeechSynthesisUtterance(text);
    
    // 应用设置
    utterance.rate = settings.rate;
    utterance.pitch = settings.pitch;
    utterance.volume = settings.volume;
    
    // 设置语音
    if (settings.voice) {
      const voice = availableVoices.find(v => v.name === settings.voice);
      if (voice) {
        utterance.voice = voice;
      }
    }

    return utterance;
  }, [settings, availableVoices]);

  const startSpeech = useCallback(() => {
    console.log('startSpeech 被调用');
    console.log('文本段落数量:', textChunksRef.current.length);
    console.log('当前段落索引:', currentChunkRef.current);
    console.log('autoNext 状态:', autoNext);
    console.log('onAutoNext 函数:', !!onAutoNext);

    if (textChunksRef.current.length === 0) {
      console.log('没有文本段落，退出');
      return;
    }

    if (isPaused) {
      console.log('恢复暂停的朗读');
      speechSynthesis.resume();
      setIsPaused(false);
      setIsPlaying(true);
      return;
    }

    const chunk = textChunksRef.current[currentChunkRef.current];
    console.log('准备朗读段落:', chunk.substring(0, 50) + '...');
    const utterance = createUtterance(chunk);

    utterance.onstart = () => {
      console.log('开始朗读段落:', currentChunkRef.current + 1, '/', textChunksRef.current.length);
      setIsPlaying(true);
      setIsPaused(false);
    };

    utterance.onend = () => {
      console.log('段落朗读结束:', currentChunkRef.current + 1, '/', textChunksRef.current.length);
      currentChunkRef.current++;
      setCurrentPosition(currentChunkRef.current);

      console.log('判断是否继续:', {
        currentChunk: currentChunkRef.current,
        totalLength: textChunksRef.current.length,
        shouldContinue: currentChunkRef.current < textChunksRef.current.length
      });

      if (currentChunkRef.current < textChunksRef.current.length) {
        // 继续下一段
        console.log('继续下一段，当前进度:', currentChunkRef.current, '/', textChunksRef.current.length);
        setTimeout(() => startSpeech(), 100);
      } else {
        // 播放完成
        console.log('🎉 所有段落朗读完成！');
        console.log('朗读完成，autoNext:', autoNext, 'onAutoNext:', !!onAutoNext);
        setIsPlaying(false);
        setCurrentPosition(0);
        currentChunkRef.current = 0;

        // 如果启用了自动跳转下一篇，则动态追加下一篇内容
        console.log('检查自动跳转条件:', {
          autoNext,
          hasOnGetNextContent: !!onGetNextContent,
          hasOnAutoNext: !!onAutoNext
        });

        if (autoNext && onGetNextContent && onAppendContent) {
          console.log('朗读完成，准备获取下一篇内容...');
          setTimeout(async () => {
            try {
              const nextContent = await onGetNextContent();
              if (nextContent) {
                console.log('获取到下一篇内容:', nextContent.title);

                // 更新当前文章标题显示
                setCurrentArticleTitle(nextContent.title);

                // 先将下一篇内容追加到页面显示（保留HTML样式）
                onAppendContent(nextContent);

                // 清理下一篇内容的HTML和Markdown格式（仅用于朗读）
                const cleanNextText = nextContent.content
                  .replace(/^---[\s\S]*?---/m, '') // 移除Front Matter
                  .replace(/!\[.*?\]\(.*?\)/g, '') // 移除图片
                  .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 移除链接，保留文本
                  .replace(/`{1,3}[^`]*`{1,3}/g, '') // 移除代码块
                  .replace(/#{1,6}\s*/g, '') // 移除标题标记
                  .replace(/[*_]{1,2}([^*_]+)[*_]{1,2}/g, '$1') // 移除粗体斜体标记
                  .replace(/^\s*[-*+]\s+/gm, '') // 移除列表标记
                  .replace(/^\s*\d+\.\s+/gm, '') // 移除数字列表标记
                  // 清理HTML标签（用于朗读）
                  .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // 移除script标签
                  .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '') // 移除style标签
                  .replace(/<[^>]+>/g, '') // 移除所有HTML标签
                  .replace(/&nbsp;/g, ' ') // 替换HTML空格
                  .replace(/&lt;/g, '<') // 替换HTML实体
                  .replace(/&gt;/g, '>') // 替换HTML实体
                  .replace(/&amp;/g, '&') // 替换HTML实体
                  .replace(/&quot;/g, '"') // 替换HTML实体
                  .replace(/&#39;/g, "'") // 替换HTML实体
                  .replace(/\n{3,}/g, '\n\n') // 合并多个换行
                  .trim();

                // 分割新内容为段落
                const newChunks = cleanNextText
                  .split(/[.!?。！？]\s+/)
                  .filter(chunk => chunk.trim().length > 0)
                  .map(chunk => chunk.trim() + '。')
                  .filter(chunk => chunk.length > 1);

                // 追加到现有的文本段落中
                textChunksRef.current = [...textChunksRef.current, ...newChunks];
                setTotalLength(textChunksRef.current.length);

                console.log('已追加下一篇内容，新的总段落数:', textChunksRef.current.length);

                // 继续朗读下一篇的第一段
                setTimeout(() => startSpeech(), 500);
              } else {
                console.log('没有更多文章了');
                setIsPlaying(false);
              }
            } catch (error) {
              console.error('获取下一篇内容失败:', error);
              setIsPlaying(false);
            }
          }, 1000); // 等待1秒后获取下一篇
        } else if (autoNext && onAutoNext) {
          // 如果没有提供onGetNextContent，则使用原来的页面跳转方式
          console.log('朗读完成，准备自动跳转到下一篇...');
          setTimeout(() => {
            setSelectedText('');
            if (typeof window !== 'undefined') {
              window.getSelection()?.removeAllRanges();
              localStorage.setItem('speechShouldAutoStart', 'true');
            }
            console.log('执行自动跳转');
            onAutoNext();
          }, 2000);
        } else {
          console.log('自动跳转未启用或回调函数不存在');
        }
      }
    };

    utterance.onerror = (event) => {
      console.error('语音合成错误:', event);
      setIsPlaying(false);
    };

    utteranceRef.current = utterance;
    speechSynthesis.speak(utterance);
  }, [autoNext, onAutoNext, onGetNextContent, onAppendContent, isPaused, createUtterance]);

  const pauseSpeech = () => {
    speechSynthesis.pause();
    setIsPaused(true);
    setIsPlaying(false);
  };

  const stopSpeech = () => {
    speechSynthesis.cancel();
    setIsPlaying(false);
    setIsPaused(false);
    setCurrentPosition(0);
    currentChunkRef.current = 0;
  };

  const skipForward = () => {
    if (currentChunkRef.current < textChunksRef.current.length - 1) {
      speechSynthesis.cancel();
      currentChunkRef.current++;
      setCurrentPosition(currentChunkRef.current);
      if (isPlaying || isPaused) {
        setTimeout(() => startSpeech(), 100);
      }
    }
  };

  const skipBackward = () => {
    if (currentChunkRef.current > 0) {
      speechSynthesis.cancel();
      currentChunkRef.current--;
      setCurrentPosition(currentChunkRef.current);
      if (isPlaying || isPaused) {
        setTimeout(() => startSpeech(), 100);
      }
    }
  };

  const handleSettingChange = (key: keyof SpeechSettings, value: number | string) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    // 保存到localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('speechSettings', JSON.stringify(newSettings));
    }
  };

  const clearSelection = () => {
    setSelectedText('');
    if (typeof window !== 'undefined') {
      window.getSelection()?.removeAllRanges();
    }
  };

  // 在服务端渲染时不显示组件，避免水合不匹配
  if (typeof window === 'undefined' || !('speechSynthesis' in window)) {
    return null;
  }

  return (
    <>
      {/* 语音控制按钮 */}
      <div className={`
        fixed bottom-6 right-6 z-40 bg-white border border-gray-200 rounded-lg shadow-lg p-3
        transition-all duration-300
        ${className}
      `}>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <Headphones className="w-4 h-4 text-gray-600" />
            <div className="flex flex-col">
              <span className="text-xs text-gray-600">语音朗读</span>
              {currentArticleTitle && (
                <span className="text-xs text-green-600 font-medium max-w-32 truncate">
                  正在朗读: {currentArticleTitle}
                </span>
              )}
              {selectedText && (
                <div className="flex items-center gap-1">
                  <span className="text-xs text-blue-600 font-medium">
                    从选中位置开始
                  </span>
                  <button
                    onClick={clearSelection}
                    className="p-0.5 hover:bg-gray-100 rounded text-gray-400 hover:text-gray-600"
                    title="清除选择"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-1">
            <button
              onClick={skipBackward}
              disabled={currentPosition === 0}
              className="p-1 hover:bg-gray-100 rounded disabled:opacity-50 disabled:cursor-not-allowed"
              title="上一段"
            >
              <SkipBack className="w-4 h-4" />
            </button>

            {isPlaying ? (
              <button
                onClick={pauseSpeech}
                className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                title="暂停"
              >
                <Pause className="w-4 h-4" />
              </button>
            ) : (
              <button
                onClick={startSpeech}
                data-speech-play-button
                className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                title={
                  isPaused
                    ? '继续'
                    : selectedText
                      ? '从选中位置播放'
                      : '播放'
                }
              >
                <Play className="w-4 h-4" />
              </button>
            )}

            <button
              onClick={stopSpeech}
              className="p-1 hover:bg-gray-100 rounded"
              title="停止"
            >
              <Square className="w-4 h-4" />
            </button>

            <button
              onClick={skipForward}
              disabled={currentPosition >= totalLength - 1}
              className="p-1 hover:bg-gray-100 rounded disabled:opacity-50 disabled:cursor-not-allowed"
              title="下一段"
            >
              <SkipForward className="w-4 h-4" />
            </button>

            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-1 hover:bg-gray-100 rounded"
              title="设置"
            >
              <Settings className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* 进度条 */}
        {totalLength > 0 && (
          <div className="mt-2">
            <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
              <span>{currentPosition + 1} / {totalLength}</span>
              <span>{Math.round((currentPosition / totalLength) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1">
              <div 
                className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                style={{ width: `${(currentPosition / totalLength) * 100}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* 设置面板 */}
      {showSettings && (
        <>
          <div
            className="fixed inset-0 bg-opacity-20 z-30"
            onClick={() => setShowSettings(false)}
          />
          <div className="fixed bottom-24 right-6 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-40 p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-gray-800">语音设置</h3>
              <button
                onClick={() => setShowSettings(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ×
              </button>
            </div>

            <div className="space-y-4">
              {/* 语音选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  语音
                </label>
                <select
                  value={settings.voice}
                  onChange={(e) => handleSettingChange('voice', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {availableVoices.map((voice) => (
                    <option key={voice.name} value={voice.name}>
                      {voice.name} ({voice.lang})
                    </option>
                  ))}
                </select>
              </div>

              {/* 语速 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  语速: {settings.rate.toFixed(1)}x
                </label>
                <input
                  type="range"
                  min="0.5"
                  max="2"
                  step="0.1"
                  value={settings.rate}
                  onChange={(e) => handleSettingChange('rate', parseFloat(e.target.value))}
                  className="w-full"
                />
              </div>

              {/* 音调 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  音调: {settings.pitch.toFixed(1)}
                </label>
                <input
                  type="range"
                  min="0.5"
                  max="2"
                  step="0.1"
                  value={settings.pitch}
                  onChange={(e) => handleSettingChange('pitch', parseFloat(e.target.value))}
                  className="w-full"
                />
              </div>

              {/* 音量 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  音量: {Math.round(settings.volume * 100)}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={settings.volume}
                  onChange={(e) => handleSettingChange('volume', parseFloat(e.target.value))}
                  className="w-full"
                />
              </div>

              {/* 自动跳转下一篇 */}
              {onAutoNext && (
                <div className="pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-700">
                      朗读完成后自动跳转下一篇
                    </label>
                    <input
                      type="checkbox"
                      checked={autoNext}
                      onChange={(e) => {
                        console.log('自动跳转设置变更:', e.target.checked);
                        setAutoNext(e.target.checked);
                        // 保存到localStorage
                        if (typeof window !== 'undefined') {
                          localStorage.setItem('speechAutoNext', e.target.checked.toString());
                        }
                      }}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    启用后，朗读完成将在2秒后自动跳转到下一篇文章
                  </p>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </>
  );
}
