'use client';

import React, { useState, useEffect } from 'react';
import {
  Globe,
  Download,
  Settings,
  FileText,
  Image,
  Loader2,
  CheckCircle,
  XCircle,
  ArrowLeft,
  Zap
} from 'lucide-react';
import Link from 'next/link';
import { crawlerPresets, getRecommendedPreset, type CrawlerPreset } from '@/lib/crawlerPresets';

interface CrawlerConfig {
  url: string;
  title?: string;
  outputDir?: string;
  selectors?: {
    title?: string;
    content?: string;
    removeElements?: string[];
  };
  batch?: {
    enabled: boolean;
    nextPageSelector?: string;
    maxPages?: number;
    delay?: number;
  };
}

interface CrawlerResult {
  success: boolean;
  message: string;
  filePath?: string;
  title?: string;
  imageCount?: number;
  pagesProcessed?: number;
  files?: string[];
}

export default function CrawlerPage() {
  const [config, setConfig] = useState<CrawlerConfig>({
    url: '',
    title: '',
    outputDir: 'blog/爬虫内容',
    selectors: {
      title: '',
      content: '',
      removeElements: []
    },
    batch: {
      enabled: false,
      nextPageSelector: '',
      maxPages: 10,
      delay: 2000
    }
  });
  
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<CrawlerResult | null>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [recommendedPreset, setRecommendedPreset] = useState<CrawlerPreset | null>(null);

  // 当URL改变时，自动推荐预设
  useEffect(() => {
    if (config.url) {
      try {
        const preset = getRecommendedPreset(config.url);
        setRecommendedPreset(preset);
      } catch (error) {
        setRecommendedPreset(null);
      }
    } else {
      setRecommendedPreset(null);
    }
  }, [config.url]);

  // 应用预设配置
  const applyPreset = (preset: CrawlerPreset) => {
    setConfig({
      ...config,
      selectors: { ...preset.selectors },
      batch: { ...preset.batch }
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!config.url) {
      alert('请输入要爬取的URL');
      return;
    }
    
    setLoading(true);
    setResult(null);
    
    try {
      const response = await fetch('/api/crawler', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });
      
      const data: CrawlerResult = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        success: false,
        message: error instanceof Error ? error.message : '请求失败'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveElementAdd = () => {
    if (!config.selectors) {
      setConfig({
        ...config,
        selectors: { removeElements: [''] }
      });
    } else {
      setConfig({
        ...config,
        selectors: {
          ...config.selectors,
          removeElements: [...(config.selectors.removeElements || []), '']
        }
      });
    }
  };

  const handleRemoveElementChange = (index: number, value: string) => {
    if (!config.selectors?.removeElements) return;
    
    const newElements = [...config.selectors.removeElements];
    newElements[index] = value;
    
    setConfig({
      ...config,
      selectors: {
        ...config.selectors,
        removeElements: newElements
      }
    });
  };

  const handleRemoveElementDelete = (index: number) => {
    if (!config.selectors?.removeElements) return;
    
    const newElements = config.selectors.removeElements.filter((_, i) => i !== index);
    
    setConfig({
      ...config,
      selectors: {
        ...config.selectors,
        removeElements: newElements
      }
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <Link 
                href="/" 
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </Link>
              <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                <Globe className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold gradient-text">网页爬虫</h1>
                <p className="text-xs text-gray-500 -mt-1">Web Crawler</p>
              </div>
            </div>
            <div className="text-sm text-gray-500 hidden sm:block bg-gray-50 px-3 py-1 rounded-full">
              抓取网页内容并转换为 Markdown
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 配置表单 */}
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">爬虫配置</h2>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* URL输入 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  目标URL *
                </label>
                <input
                  type="url"
                  value={config.url}
                  onChange={(e) => setConfig({ ...config, url: e.target.value })}
                  placeholder="https://example.com/article"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>

              {/* 标题输入 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  文章标题 (可选)
                </label>
                <input
                  type="text"
                  value={config.title || ''}
                  onChange={(e) => setConfig({ ...config, title: e.target.value })}
                  placeholder="留空则自动提取"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* 输出目录 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  输出目录
                </label>
                <input
                  type="text"
                  value={config.outputDir || ''}
                  onChange={(e) => setConfig({ ...config, outputDir: e.target.value })}
                  placeholder="blog/爬虫内容"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* 预设配置 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  预设配置
                </label>
                <div className="space-y-2">
                  {/* 推荐预设 */}
                  {recommendedPreset && (
                    <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <Zap className="w-4 h-4 text-green-600" />
                            <span className="text-sm font-medium text-green-800">
                              推荐: {recommendedPreset.name}
                            </span>
                          </div>
                          <p className="text-xs text-green-600 mt-1">
                            {recommendedPreset.description}
                          </p>
                        </div>
                        <button
                          type="button"
                          onClick={() => applyPreset(recommendedPreset)}
                          className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors"
                        >
                          应用
                        </button>
                      </div>
                    </div>
                  )}

                  {/* 预设选择器 */}
                  <select
                    onChange={(e) => {
                      const preset = crawlerPresets.find(p => p.name === e.target.value);
                      if (preset) applyPreset(preset);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    defaultValue=""
                  >
                    <option value="">选择预设配置...</option>
                    {crawlerPresets.map((preset) => (
                      <option key={preset.name} value={preset.name}>
                        {preset.name} - {preset.description}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* 高级设置 */}
              <div>
                <button
                  type="button"
                  onClick={() => setShowAdvanced(!showAdvanced)}
                  className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800"
                >
                  <Settings className="w-4 h-4" />
                  高级设置
                </button>
              </div>

              {showAdvanced && (
                <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                  {/* 标题选择器 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      标题选择器 (CSS Selector)
                    </label>
                    <input
                      type="text"
                      value={config.selectors?.title || ''}
                      onChange={(e) => setConfig({
                        ...config,
                        selectors: { ...config.selectors, title: e.target.value }
                      })}
                      placeholder="h1, .title, .article-title"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  {/* 内容选择器 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      内容选择器 (CSS Selector)
                    </label>
                    <input
                      type="text"
                      value={config.selectors?.content || ''}
                      onChange={(e) => setConfig({
                        ...config,
                        selectors: { ...config.selectors, content: e.target.value }
                      })}
                      placeholder=".content, .article-content, main"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  {/* 移除元素选择器 */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <label className="block text-sm font-medium text-gray-700">
                        移除元素选择器
                      </label>
                      <button
                        type="button"
                        onClick={handleRemoveElementAdd}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        + 添加
                      </button>
                    </div>
                    {config.selectors?.removeElements?.map((element, index) => (
                      <div key={index} className="flex gap-2 mb-2">
                        <input
                          type="text"
                          value={element}
                          onChange={(e) => handleRemoveElementChange(index, e.target.value)}
                          placeholder=".ads, .sidebar, .comment"
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        <button
                          type="button"
                          onClick={() => handleRemoveElementDelete(index)}
                          className="px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg"
                        >
                          删除
                        </button>
                      </div>
                    ))}
                  </div>

                  {/* 批量爬取设置 */}
                  <div className="border-t pt-4">
                    <div className="flex items-center gap-2 mb-4">
                      <input
                        type="checkbox"
                        id="batchEnabled"
                        checked={config.batch?.enabled || false}
                        onChange={(e) => setConfig({
                          ...config,
                          batch: { ...config.batch, enabled: e.target.checked }
                        })}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor="batchEnabled" className="text-sm font-medium text-gray-700">
                        启用批量爬取 (适用于小说章节等连续内容)
                      </label>
                    </div>

                    {config.batch?.enabled && (
                      <div className="space-y-3 pl-6 border-l-2 border-blue-200">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            下一页选择器
                          </label>
                          <input
                            type="text"
                            value={config.batch?.nextPageSelector || ''}
                            onChange={(e) => setConfig({
                              ...config,
                              batch: { ...config.batch, nextPageSelector: e.target.value }
                            })}
                            placeholder="a[href*='next'], .next, .next-page"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              最大页数
                            </label>
                            <input
                              type="number"
                              min="1"
                              max="100"
                              value={config.batch?.maxPages || 10}
                              onChange={(e) => setConfig({
                                ...config,
                                batch: { ...config.batch, maxPages: parseInt(e.target.value) || 10 }
                              })}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              延迟 (毫秒)
                            </label>
                            <input
                              type="number"
                              min="1000"
                              max="10000"
                              step="500"
                              value={config.batch?.delay || 2000}
                              onChange={(e) => setConfig({
                                ...config,
                                batch: { ...config.batch, delay: parseInt(e.target.value) || 2000 }
                              })}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* 提交按钮 */}
              <button
                type="submit"
                disabled={loading}
                className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin" />
                    爬取中...
                  </>
                ) : (
                  <>
                    <Download className="w-5 h-5" />
                    开始爬取
                  </>
                )}
              </button>
            </form>
          </div>

          {/* 结果显示 */}
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">爬取结果</h2>
            
            {!result && !loading && (
              <div className="text-center py-12 text-gray-500">
                <Globe className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                <p>配置爬虫参数并开始爬取</p>
              </div>
            )}

            {loading && (
              <div className="text-center py-12">
                <Loader2 className="w-12 h-12 mx-auto mb-4 text-blue-500 animate-spin" />
                <p className="text-gray-600">正在爬取网页内容...</p>
                <p className="text-sm text-gray-500 mt-2">请耐心等待，这可能需要一些时间</p>
              </div>
            )}

            {result && (
              <div className="space-y-4">
                {/* 状态指示 */}
                <div className={`flex items-center gap-3 p-4 rounded-lg ${
                  result.success 
                    ? 'bg-green-50 border border-green-200' 
                    : 'bg-red-50 border border-red-200'
                }`}>
                  {result.success ? (
                    <CheckCircle className="w-6 h-6 text-green-600" />
                  ) : (
                    <XCircle className="w-6 h-6 text-red-600" />
                  )}
                  <div>
                    <p className={`font-medium ${
                      result.success ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {result.success ? '爬取成功' : '爬取失败'}
                    </p>
                    <p className={`text-sm ${
                      result.success ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {result.message}
                    </p>
                  </div>
                </div>

                {/* 成功结果详情 */}
                {result.success && (
                  <div className="space-y-3">
                    {result.title && (
                      <div className="flex items-center gap-2">
                        <FileText className="w-5 h-5 text-blue-500" />
                        <span className="text-sm text-gray-600">标题:</span>
                        <span className="font-medium">{result.title}</span>
                      </div>
                    )}

                    {result.filePath && (
                      <div className="flex items-center gap-2">
                        <Download className="w-5 h-5 text-green-500" />
                        <span className="text-sm text-gray-600">文件:</span>
                        <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                          {result.filePath}
                        </span>
                      </div>
                    )}

                    {result.pagesProcessed && (
                      <div className="flex items-center gap-2">
                        <Globe className="w-5 h-5 text-indigo-500" />
                        <span className="text-sm text-gray-600">页数:</span>
                        <span className="font-medium">{result.pagesProcessed} 页</span>
                      </div>
                    )}

                    {result.files && result.files.length > 0 && (
                      <div>
                        <div className="flex items-center gap-2 mb-2">
                          <FileText className="w-5 h-5 text-blue-500" />
                          <span className="text-sm text-gray-600">生成文件:</span>
                        </div>
                        <div className="max-h-32 overflow-y-auto bg-gray-50 rounded p-2">
                          {result.files.map((file, index) => (
                            <div key={index} className="font-mono text-xs text-gray-600 mb-1">
                              {file}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {typeof result.imageCount === 'number' && (
                      <div className="flex items-center gap-2">
                        <Image className="w-5 h-5 text-purple-500" />
                        <span className="text-sm text-gray-600">图片:</span>
                        <span className="font-medium">{result.imageCount} 张</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* 使用说明 */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 mb-3">使用说明</h3>
          <div className="text-sm text-blue-700 space-y-2">
            <p>• <strong>基本使用:</strong> 只需输入目标URL即可开始爬取</p>
            <p>• <strong>自动提取:</strong> 系统会自动识别标题和正文内容</p>
            <p>• <strong>图片处理:</strong> 自动下载图片到本地，避免链接失效</p>
            <p>• <strong>高级设置:</strong> 可自定义CSS选择器来精确提取内容</p>
            <p>• <strong>批量爬取:</strong> 适用于小说章节等连续内容，自动跟随下一页链接</p>
            <p>• <strong>输出格式:</strong> 生成带有Front Matter的Markdown文件</p>
            <p>• <strong>注意事项:</strong> 请遵守网站的robots.txt和使用条款，合理控制爬取频率</p>
          </div>
        </div>
      </main>
    </div>
  );
}
