---
title: React基础知识
date: 2023-07-15
author: 朱鹏亮
---

# React基础知识

React是一个用于构建用户界面的JavaScript库。它由Facebook开发，并于2013年开源。

## 核心概念

### 组件

React应用由组件构成。组件是React的核心概念，它们是自包含的、可重用的代码片段，用于定义UI的一部分。

```jsx
function Welcome(props) {
  return <h1>Hello, {props.name}</h1>;
}
```

### JSX

JSX是JavaScript的语法扩展，它看起来像HTML，但实际上是JavaScript。

```jsx
const element = <h1>Hello, world!</h1>;
```

### Props

Props是从父组件传递到子组件的数据。

```jsx
function Welcome(props) {
  return <h1>Hello, {props.name}</h1>;
}

const element = <Welcome name="Sara" />;
```

### State

State是组件内部管理的数据，当state改变时，组件会重新渲染。

```jsx
import React, { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0);

  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  );
}
```

## 生命周期

React组件有多个生命周期方法，它们在组件的不同阶段被调用。

### 挂载

当组件被创建并插入DOM中时，这些方法按以下顺序调用：

1. constructor()
2. static getDerivedStateFromProps()
3. render()
4. componentDidMount()

### 更新

当组件的props或state发生变化时，会触发更新。这些方法按以下顺序调用：

1. static getDerivedStateFromProps()
2. shouldComponentUpdate()
3. render()
4. getSnapshotBeforeUpdate()
5. componentDidUpdate()

### 卸载

当组件从DOM中移除时，会调用此方法：

1. componentWillUnmount()

## Hooks

Hooks是React 16.8中引入的新特性，它允许你在不编写类的情况下使用state和其他React特性。

### useState

```jsx
import React, { useState } from 'react';

function Example() {
  const [count, setCount] = useState(0);

  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  );
}
```

### useEffect

```jsx
import React, { useState, useEffect } from 'react';

function Example() {
  const [count, setCount] = useState(0);

  useEffect(() => {
    document.title = `You clicked ${count} times`;
  });

  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  );
}
```

## 总结

React是一个强大的JavaScript库，它使用组件、props和state来构建用户界面。通过学习这些基础概念，你可以开始构建自己的React应用。
