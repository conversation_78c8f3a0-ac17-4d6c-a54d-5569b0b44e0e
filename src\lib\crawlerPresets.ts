// 爬虫预设配置
export interface CrawlerPreset {
  name: string;
  description: string;
  selectors: {
    title?: string;
    content?: string;
    removeElements?: string[];
  };
  batch?: {
    enabled: boolean;
    nextPageSelector?: string;
    maxPages?: number;
    delay?: number;
  };
}

export const crawlerPresets: CrawlerPreset[] = [
  {
    name: '通用网页',
    description: '适用于大多数网页的通用配置',
    selectors: {
      title: 'h1, .title, .article-title, title',
      content: '.content, .article-content, .post-content, main, article',
      removeElements: [
        'script', 'style', 'nav', 'header', 'footer',
        '.advertisement', '.ads', '.sidebar', '.menu',
        '.comment', '.share', '.social', '.related'
      ]
    },
    batch: {
      enabled: false,
      nextPageSelector: 'a[href*="next"], .next, .next-page',
      maxPages: 10,
      delay: 2000
    }
  },
  {
    name: '小说网站',
    description: '适用于小说阅读网站的配置',
    selectors: {
      title: '.chapter-title, .title, h1, .book-title',
      content: '.chapter-content, .content, .text-content, #content',
      removeElements: [
        'script', 'style', 'nav', 'header', 'footer',
        '.advertisement', '.ads', '.sidebar', '.menu',
        '.comment', '.share', '.social', '.related',
        '.chapter-nav', '.book-nav', '.vote', '.reward'
      ]
    },
    batch: {
      enabled: true,
      nextPageSelector: '.next-chapter, .next, a[href*="next"], a[contains(text(), "下一章")]',
      maxPages: 50,
      delay: 3000
    }
  },
  {
    name: '博客文章',
    description: '适用于博客文章的配置',
    selectors: {
      title: '.post-title, .entry-title, h1, .article-title',
      content: '.post-content, .entry-content, .article-content, .content',
      removeElements: [
        'script', 'style', 'nav', 'header', 'footer',
        '.advertisement', '.ads', '.sidebar', '.widget',
        '.comment', '.share', '.social', '.related',
        '.author-bio', '.tags', '.categories'
      ]
    },
    batch: {
      enabled: false,
      nextPageSelector: '',
      maxPages: 1,
      delay: 2000
    }
  },
  {
    name: '新闻文章',
    description: '适用于新闻网站的配置',
    selectors: {
      title: '.headline, .news-title, h1, .article-title',
      content: '.article-body, .news-content, .content, .story-body',
      removeElements: [
        'script', 'style', 'nav', 'header', 'footer',
        '.advertisement', '.ads', '.sidebar', '.widget',
        '.comment', '.share', '.social', '.related',
        '.author-info', '.publish-time', '.tags'
      ]
    },
    batch: {
      enabled: false,
      nextPageSelector: '',
      maxPages: 1,
      delay: 2000
    }
  },
  {
    name: '论坛帖子',
    description: '适用于论坛帖子的配置',
    selectors: {
      title: '.thread-title, .topic-title, h1, .post-title',
      content: '.post-content, .thread-content, .message-content, .content',
      removeElements: [
        'script', 'style', 'nav', 'header', 'footer',
        '.advertisement', '.ads', '.sidebar', '.widget',
        '.signature', '.user-info', '.post-actions',
        '.quote', '.reply', '.edit-info'
      ]
    },
    batch: {
      enabled: true,
      nextPageSelector: '.next-page, a[href*="page"], .pagination .next',
      maxPages: 20,
      delay: 2500
    }
  }
];

// 根据URL自动推荐预设
export function getRecommendedPreset(url: string): CrawlerPreset | null {
  const domain = new URL(url).hostname.toLowerCase();
  
  // 小说网站关键词
  const novelKeywords = ['novel', 'book', 'read', 'chapter', '小说', '阅读', '章节'];
  if (novelKeywords.some(keyword => domain.includes(keyword))) {
    return crawlerPresets.find(p => p.name === '小说网站') || null;
  }
  
  // 博客关键词
  const blogKeywords = ['blog', 'post', 'article', '博客', '文章'];
  if (blogKeywords.some(keyword => domain.includes(keyword))) {
    return crawlerPresets.find(p => p.name === '博客文章') || null;
  }
  
  // 新闻关键词
  const newsKeywords = ['news', 'press', 'media', '新闻', '资讯'];
  if (newsKeywords.some(keyword => domain.includes(keyword))) {
    return crawlerPresets.find(p => p.name === '新闻文章') || null;
  }
  
  // 论坛关键词
  const forumKeywords = ['forum', 'bbs', 'community', '论坛', '社区'];
  if (forumKeywords.some(keyword => domain.includes(keyword))) {
    return crawlerPresets.find(p => p.name === '论坛帖子') || null;
  }
  
  // 默认返回通用配置
  return crawlerPresets.find(p => p.name === '通用网页') || null;
}
