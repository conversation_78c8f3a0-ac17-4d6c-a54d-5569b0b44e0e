@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
  border: 2px solid #f8f9fa;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Markdown 内容样式优化 */
.prose {
  max-width: none;
}

.prose h1 {
  @apply text-3xl font-bold text-gray-900 mb-6 pb-2 border-b border-gray-200;
}

.prose h2 {
  @apply text-2xl font-semibold text-gray-800 mb-4 mt-8;
}

.prose h3 {
  @apply text-xl font-semibold text-gray-800 mb-3 mt-6;
}

.prose h4 {
  @apply text-lg font-semibold text-gray-800 mb-2 mt-4;
}

.prose p {
  @apply text-gray-700 leading-relaxed mb-4;
}

.prose ul, .prose ol {
  @apply mb-4 pl-6;
}

.prose li {
  @apply mb-2 text-gray-700;
}

.prose blockquote {
  @apply border-l-4 border-blue-500 pl-4 py-2 my-4 bg-blue-50 text-gray-700 italic;
}

.prose code {
  @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono text-gray-800;
}

.prose pre {
  @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto my-4;
}

.prose pre code {
  @apply bg-transparent p-0 text-gray-100;
}

.prose a {
  @apply text-blue-600 hover:text-blue-800 underline;
}

.prose img {
  @apply rounded-lg shadow-md my-4;
}

.prose table {
  @apply w-full border-collapse border border-gray-300 my-4;
}

.prose th {
  @apply bg-gray-50 border border-gray-300 px-4 py-2 text-left font-semibold;
}

.prose td {
  @apply border border-gray-300 px-4 py-2;
}

/* 简化的动画效果 */
.fade-in {
  opacity: 1;
  transition: opacity 0.2s ease-in-out;
}

/* 卡片悬停效果 */
.card-hover {
  transition: box-shadow 0.2s ease-in-out;
}

.card-hover:hover {
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1);
}

/* 按钮动画 */
.btn-animate {
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.btn-animate:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-animate:active {
  transform: translateY(0);
}

/* 文件项悬停效果 */
.file-item {
  transition: all 0.15s ease-in-out;
  position: relative;
}

.file-item:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-left: 3px solid #3b82f6;
  padding-left: calc(var(--padding-left) - 3px);
}

.file-item.selected {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-left: 4px solid #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

/* 加载动画优化 */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 阅读主题 */
.theme-light {
  --reading-bg: #ffffff;
  --reading-text: #1f2937;
  --reading-text-light: #6b7280;
  --reading-border: #e5e7eb;
  --reading-code-bg: #f3f4f6;
}

.theme-dark {
  --reading-bg: #1f2937;
  --reading-text: #f9fafb;
  --reading-text-light: #d1d5db;
  --reading-border: #374151;
  --reading-code-bg: #111827;
}

.theme-sepia {
  --reading-bg: #fdf6e3;
  --reading-text: #5d4037;
  --reading-text-light: #8d6e63;
  --reading-border: #d7ccc8;
  --reading-code-bg: #efebe9;
}

/* 应用阅读主题 */
.theme-dark article,
.theme-sepia article {
  background-color: var(--reading-bg);
  color: var(--reading-text);
}

.theme-dark article .prose,
.theme-sepia article .prose {
  color: var(--reading-text);
}

.theme-dark article .prose h1,
.theme-dark article .prose h2,
.theme-dark article .prose h3,
.theme-dark article .prose h4,
.theme-sepia article .prose h1,
.theme-sepia article .prose h2,
.theme-sepia article .prose h3,
.theme-sepia article .prose h4 {
  color: var(--reading-text);
}

.theme-dark article .prose p,
.theme-dark article .prose li,
.theme-sepia article .prose p,
.theme-sepia article .prose li {
  color: var(--reading-text-light);
}

.theme-dark article .prose code,
.theme-sepia article .prose code {
  background-color: var(--reading-code-bg);
  color: var(--reading-text);
}

.theme-dark article .prose pre,
.theme-sepia article .prose pre {
  background-color: var(--reading-code-bg);
}

.theme-dark article .prose blockquote,
.theme-sepia article .prose blockquote {
  border-left-color: var(--reading-border);
  background-color: var(--reading-code-bg);
  color: var(--reading-text-light);
}

.theme-dark article .prose table,
.theme-sepia article .prose table {
  border-color: var(--reading-border);
}

.theme-dark article .prose th,
.theme-dark article .prose td,
.theme-sepia article .prose th,
.theme-sepia article .prose td {
  border-color: var(--reading-border);
  color: var(--reading-text);
}

.theme-dark article .prose th,
.theme-sepia article .prose th {
  background-color: var(--reading-code-bg);
}

/* 文件预览悬停效果 */
.card-hover:hover .group-hover\:opacity-100 {
  opacity: 1;
}
