# 博客系统 - Book Master

一个基于 Next.js 15 的现代化博客系统，支持动态读取和预览 Markdown 和 Word 文件。

## ✨ 特性

- 📁 **动态文件读取** - 自动扫描 `public/blog/` 文件夹
- 🌳 **层级结构** - 完整保持文件夹层级关系
- 📝 **多格式支持** - Markdown (.md)、Word (.docx)、文本 (.txt)
- ⚡ **实时预览** - 点击文件即时预览内容
- 📖 **阅读模式** - 专门的阅读界面，提供最佳阅读体验
- 🔖 **收藏功能** - 收藏喜欢的文章，方便日后查找
- 🎨 **美观界面** - 现代化设计，响应式布局
- 🔍 **元数据支持** - 自动解析 Markdown Front matter
- 🕷️ **网页爬虫** - 抓取网页内容并转换为 Markdown
- 📱 **移动友好** - 适配不同屏幕尺寸
- 🎭 **动画效果** - 流畅的交互动画

## 🚀 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 添加博客文章

1. 在 `public/blog/` 文件夹中创建你的文章
2. 支持的文件格式：`.md`、`.docx`、`.txt`
3. 可以创建子文件夹来组织文章
4. 刷新页面即可看到新文章

## 📁 文件结构

```
public/blog/
├── 技术文档/
│   ├── React基础.md
│   └── 前端开发/
│       └── CSS布局技巧.md
├── 生活随笔/
│   └── 今日感想.md
└── 项目笔记/
    └── Next.js博客系统.md
```

## 🛠 技术栈

- **前端框架**: Next.js 15 (App Router)
- **UI库**: React 19
- **样式**: Tailwind CSS 4
- **语言**: TypeScript
- **Markdown解析**: react-markdown + remark-gfm
- **Word文件处理**: mammoth
- **图标**: Lucide React
- **元数据解析**: gray-matter

## 📝 Markdown Front Matter 支持

在 Markdown 文件开头添加元数据：

```markdown
---
title: 文章标题
date: 2023-07-21
author: 作者名
category: 分类
tags: [标签1, 标签2]
---

# 文章内容
```

## 📖 阅读功能

- **专业阅读界面** - 优化的阅读布局和排版
- **阅读进度跟踪** - 实时显示阅读进度条
- **目录导航** - 自动生成文章目录，快速跳转
- **阅读设置** - 字体大小、字体样式、主题切换
- **收藏管理** - 收藏喜欢的文章，统一管理
- **文章导航** - 上一篇/下一篇快速切换
- **分享功能** - 一键分享文章链接
- **多主题支持** - 浅色、深色、护眼三种主题
- **语音朗读** - 智能文本转语音，支持多种语音引擎

## 🎵 语音朗读功能

- **智能文本处理** - 自动清理Markdown格式，提取纯文本
- **分段朗读** - 智能分割长文本，提高朗读质量
- **完整播放控制** - 播放/暂停/停止/跳转控制
- **个性化设置** - 语音选择、语速调节、音调控制、音量调节
- **进度显示** - 实时显示朗读进度和完成百分比
- **自动跳转** - 朗读完成后自动跳转到同文件夹下一篇文章
- **多设备支持** - 兼容桌面和移动设备浏览器
- **中文优化** - 优先选择中文语音引擎
- **快捷访问** - 预览面板悬停显示阅读模式和语音按钮

## 🎨 样式特性

- **渐变背景** - 美观的渐变色彩
- **悬停效果** - 卡片和按钮的悬停动画
- **自定义滚动条** - 优化的滚动体验
- **响应式设计** - 适配桌面和移动设备
- **加载动画** - 流畅的加载状态

## 📱 响应式设计

- **桌面端**: 左侧文件列表，右侧内容预览
- **移动端**: 垂直布局，优化触摸体验
- **自适应高度**: 文件列表高度根据屏幕动态调整

## 🔧 自定义配置

### 修改博客文件夹路径

在 `src/app/page.tsx` 中修改：

```typescript
const response = await fetch('/api/files?path=your-folder');
```

### 添加新的文件类型支持

在 `src/app/api/file-content/route.ts` 中添加新的文件处理逻辑。

## 📦 构建和部署

```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
