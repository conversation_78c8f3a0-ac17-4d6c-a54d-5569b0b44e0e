import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// 文件类型接口
export interface FileItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  extension?: string;
  children?: FileItem[];
}

// 获取文件列表
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const dirPath = searchParams.get('path') || 'blog';
    
    // 构建完整路径（相对于public文件夹）
    const fullPath = path.join(process.cwd(), 'public', dirPath);
    
    // 检查路径是否存在
    if (!fs.existsSync(fullPath)) {
      return NextResponse.json(
        { error: 'Path does not exist' },
        { status: 404 }
      );
    }
    
    // 检查是否是目录
    const stats = fs.statSync(fullPath);
    if (!stats.isDirectory()) {
      return NextResponse.json(
        { error: 'Path is not a directory' },
        { status: 400 }
      );
    }
    
    // 递归读取文件夹内容
    const files = readDirectoryRecursive(fullPath, dirPath);
    
    return NextResponse.json(files);
  } catch (error) {
    console.error('Error reading files:', error);
    return NextResponse.json(
      { error: 'Failed to read files' },
      { status: 500 }
    );
  }
}

// 递归读取目录函数
function readDirectoryRecursive(fullPath: string, relativePath: string): FileItem[] {
  const items = fs.readdirSync(fullPath);
  
  return items.map(item => {
    const itemFullPath = path.join(fullPath, item);
    const itemRelativePath = path.join(relativePath, item).replace(/\\/g, '/');
    const stats = fs.statSync(itemFullPath);
    
    if (stats.isDirectory()) {
      return {
        name: item,
        path: itemRelativePath,
        type: 'directory',
        children: readDirectoryRecursive(itemFullPath, itemRelativePath)
      };
    } else {
      const extension = path.extname(item).toLowerCase();
      return {
        name: item,
        path: itemRelativePath,
        type: 'file',
        extension: extension.slice(1) // 移除前导点
      };
    }
  }).sort((a, b) => {
    // 目录排在文件前面
    if (a.type !== b.type) {
      return a.type === 'directory' ? -1 : 1;
    }
    // 同类型按名称排序
    return a.name.localeCompare(b.name);
  });
}
