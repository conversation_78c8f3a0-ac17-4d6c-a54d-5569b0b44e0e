'use client';

import React, { useState, useEffect } from 'react';
import { Volume2, VolumeX, Play, Pause } from 'lucide-react';

interface SpeechButtonProps {
  text: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export default function SpeechButton({ 
  text, 
  className = '', 
  size = 'md' 
}: SpeechButtonProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isSupported, setIsSupported] = useState(false);

  useEffect(() => {
    setIsSupported('speechSynthesis' in window);
    
    return () => {
      if (speechSynthesis.speaking) {
        speechSynthesis.cancel();
      }
    };
  }, []);

  const toggleSpeech = () => {
    if (!isSupported || !text) return;

    if (isPlaying) {
      speechSynthesis.cancel();
      setIsPlaying(false);
    } else {
      // 清理文本
      const cleanText = text
        .replace(/^---[\s\S]*?---/m, '') // 移除Front Matter
        .replace(/!\[.*?\]\(.*?\)/g, '') // 移除图片
        .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 移除链接，保留文本
        .replace(/`{1,3}[^`]*`{1,3}/g, '') // 移除代码块
        .replace(/#{1,6}\s*/g, '') // 移除标题标记
        .replace(/[*_]{1,2}([^*_]+)[*_]{1,2}/g, '$1') // 移除粗体斜体标记
        .replace(/^\s*[-*+]\s+/gm, '') // 移除列表标记
        .replace(/^\s*\d+\.\s+/gm, '') // 移除数字列表标记
        .replace(/\n{3,}/g, '\n\n') // 合并多个换行
        .trim();

      if (cleanText) {
        const utterance = new SpeechSynthesisUtterance(cleanText);
        
        // 设置语音参数
        utterance.rate = 1;
        utterance.pitch = 1;
        utterance.volume = 0.8;
        
        // 优先选择中文语音
        const voices = speechSynthesis.getVoices();
        const chineseVoice = voices.find(voice => 
          voice.lang.includes('zh') || voice.name.includes('Chinese')
        );
        if (chineseVoice) {
          utterance.voice = chineseVoice;
        }

        utterance.onstart = () => setIsPlaying(true);
        utterance.onend = () => setIsPlaying(false);
        utterance.onerror = () => setIsPlaying(false);

        speechSynthesis.speak(utterance);
      }
    }
  };

  if (!isSupported) {
    return null;
  }

  const sizeClasses = {
    sm: 'p-1',
    md: 'p-2',
    lg: 'p-3'
  };

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  return (
    <button
      onClick={toggleSpeech}
      className={`
        ${sizeClasses[size]} 
        hover:bg-gray-100 rounded-lg transition-colors
        ${isPlaying ? 'text-blue-600' : 'text-gray-600'}
        ${className}
      `}
      title={isPlaying ? '停止朗读' : '语音朗读'}
    >
      {isPlaying ? (
        <Pause className={iconSizes[size]} />
      ) : (
        <Volume2 className={iconSizes[size]} />
      )}
    </button>
  );
}
