{"name": "book-master", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"axios": "^1.10.0", "cheerio": "^1.1.1", "gray-matter": "^4.0.3", "jsdom": "^26.1.0", "lucide-react": "^0.525.0", "mammoth": "^1.9.1", "next": "15.4.2", "react": "19.1.0", "react-dom": "19.1.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "turndown": "^7.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "typescript": "^5"}}